#!/usr/bin/env python3
"""Basic test script for AI Coding CLI.

This script tests the basic functionality of the AI coding CLI tool
without requiring an OpenAI API key.
"""

import os
import sys
from pathlib import Path

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent / "src"))

from ai_coding_cli.config import Config, load_config
from ai_coding_cli.presets import PresetManager
from ai_coding_cli.tools.registry import ToolRegistry
from ai_coding_cli.tools.file_operations import FileOperationsTool
from ai_coding_cli.tools.code_analysis import CodeAnalysisTool


def test_config():
    """Test configuration loading and management."""
    print("Testing configuration...")
    
    # Test default config
    config = Config()
    assert config.openai_model == "gpt-4o-mini"
    assert config.max_file_size == 1048576
    assert config.safe_mode == True
    
    # Test config display (should not crash)
    config.display()
    
    print("✓ Configuration test passed")


def test_presets():
    """Test preset management."""
    print("Testing presets...")
    
    # Create temporary config directory
    config_dir = Path("/tmp/ai-coding-cli-test")
    config_dir.mkdir(exist_ok=True)
    
    preset_manager = PresetManager(config_dir)
    
    # Test listing presets (should have defaults)
    presets = preset_manager.list_presets()
    assert len(presets) > 0
    assert "test" in presets
    
    # Test getting a preset
    test_preset = preset_manager.get_preset("test")
    assert test_preset is not None
    assert "unit tests" in test_preset.lower()
    
    # Test creating a custom preset
    success = preset_manager.create_preset(
        "custom_test",
        "This is a test preset with ${variable}",
        "Test preset description"
    )
    assert success
    
    # Test rendering preset
    rendered = preset_manager.render_preset(
        "Hello ${name}, you have ${count} items",
        {"name": "World", "count": 5}
    )
    assert rendered == "Hello World, you have 5 items"
    
    print("✓ Presets test passed")


def test_tools():
    """Test tool system."""
    print("Testing tools...")
    
    # Create config
    config = Config()
    
    # Test tool registry
    registry = ToolRegistry(config)
    
    # Check that tools are registered
    tools = registry.list_tools()
    assert "file_operations" in tools
    assert "code_analysis" in tools
    
    # Test getting OpenAI schemas
    schemas = registry.get_openai_tools()
    assert len(schemas) > 0
    
    # Test file operations tool
    file_tool = registry.get_tool("file_operations")
    assert file_tool is not None
    assert file_tool.name == "file_operations"
    
    # Test tool validation
    assert registry.validate_all_tools()
    
    print("✓ Tools test passed")


def test_file_operations():
    """Test file operations tool."""
    print("Testing file operations...")
    
    config = Config()
    file_tool = FileOperationsTool(config)
    
    # Test creating a temporary file
    test_file = "/tmp/ai-coding-test.txt"
    test_content = "Hello, AI Coding CLI!"
    
    # Test file creation
    result = file_tool.execute({
        "operation": "create",
        "path": test_file,
        "content": test_content
    }, interactive=False)
    
    assert result.success
    assert test_file in result.created_files
    
    # Test file reading
    result = file_tool.execute({
        "operation": "read",
        "path": test_file
    }, interactive=False)
    
    assert result.success
    assert result.data["content"] == test_content
    
    # Test file existence check
    result = file_tool.execute({
        "operation": "exists",
        "path": test_file
    }, interactive=False)
    
    assert result.success
    assert result.data["exists"] == True
    
    # Clean up
    result = file_tool.execute({
        "operation": "delete",
        "path": test_file
    }, interactive=False)
    
    assert result.success
    
    print("✓ File operations test passed")


def test_code_analysis():
    """Test code analysis tool."""
    print("Testing code analysis...")
    
    config = Config()
    analysis_tool = CodeAnalysisTool(config)
    
    # Create a test Python file
    test_file = "/tmp/test_code.py"
    test_code = '''
def hello_world(name="World"):
    """Say hello to someone."""
    return f"Hello, {name}!"

class TestClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value

import os
import sys
from pathlib import Path
'''
    
    with open(test_file, 'w') as f:
        f.write(test_code)
    
    try:
        # Test syntax analysis
        result = analysis_tool.execute({
            "analysis_type": "syntax",
            "path": test_file
        }, interactive=False)
        
        assert result.success
        assert result.data["summary"]["valid"] == 1
        
        # Test import analysis
        result = analysis_tool.execute({
            "analysis_type": "imports",
            "path": test_file
        }, interactive=False)
        
        assert result.success
        imports = result.data["all_imports"]
        assert "os" in imports
        assert "sys" in imports
        assert "pathlib" in imports
        
        # Test function analysis
        result = analysis_tool.execute({
            "analysis_type": "functions",
            "path": test_file
        }, interactive=False)
        
        assert result.success
        functions = result.data["results"][0]["functions"]
        function_names = [f["name"] for f in functions]
        assert "hello_world" in function_names
        assert "__init__" in function_names
        assert "get_value" in function_names
        
    finally:
        # Clean up
        os.unlink(test_file)
    
    print("✓ Code analysis test passed")


def main():
    """Run all tests."""
    print("Running AI Coding CLI basic tests...\n")
    
    try:
        test_config()
        test_presets()
        test_tools()
        test_file_operations()
        test_code_analysis()
        
        print("\n🎉 All tests passed!")
        print("\nNext steps:")
        print("1. Set your OpenAI API key: export OPENAI_API_KEY=your_key_here")
        print("2. Install the package: uv run python -m pip install -e .")
        print("3. Try the CLI: uv run ai-code ask 'Create a hello world function'")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
