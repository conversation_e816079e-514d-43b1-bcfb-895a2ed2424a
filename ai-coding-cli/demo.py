#!/usr/bin/env python3
"""Demo script for AI Coding CLI.

This script demonstrates the capabilities of the AI coding CLI tool
without requiring an OpenAI API key.
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description):
    """Run a command and display the result."""
    print(f"\n{'='*60}")
    print(f"🔧 {description}")
    print(f"{'='*60}")
    print(f"Command: {cmd}")
    print("-" * 60)
    
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running command: {e}")
        return False


def main():
    """Run the demo."""
    print("🚀 AI Coding CLI Demo")
    print("=" * 60)
    print("This demo shows the basic functionality of the AI Coding CLI tool.")
    print("Note: OpenAI API features require a valid API key.")
    print()
    
    # Check if we're in the right directory
    if not Path("src/ai_coding_cli").exists():
        print("❌ Please run this script from the ai-coding-cli directory")
        sys.exit(1)
    
    demos = [
        {
            "cmd": "uv run python test_basic.py",
            "desc": "Running basic functionality tests"
        },
        {
            "cmd": "uv run python -m ai_coding_cli.cli --help",
            "desc": "Showing CLI help"
        },
        {
            "cmd": "uv run python -m ai_coding_cli.cli preset list",
            "desc": "Listing available presets"
        },
        {
            "cmd": "uv run python -m ai_coding_cli.cli preset show test",
            "desc": "Showing the 'test' preset template"
        },
        {
            "cmd": "uv run python -m ai_coding_cli.cli preset show refactor",
            "desc": "Showing the 'refactor' preset template"
        },
        {
            "cmd": "uv run python -m ai_coding_cli.cli config show",
            "desc": "Showing current configuration"
        },
    ]
    
    success_count = 0
    
    for demo in demos:
        if run_command(demo["cmd"], demo["desc"]):
            success_count += 1
        else:
            print("❌ Command failed")
    
    print(f"\n{'='*60}")
    print(f"📊 Demo Results: {success_count}/{len(demos)} commands succeeded")
    print(f"{'='*60}")
    
    if success_count == len(demos):
        print("🎉 All demo commands completed successfully!")
        print("\n📝 Next Steps:")
        print("1. Set your OpenAI API key:")
        print("   export OPENAI_API_KEY=your_key_here")
        print("\n2. Try some AI-powered commands:")
        print("   uv run python -m ai_coding_cli.cli ask 'Create a hello world function'")
        print("   uv run python -m ai_coding_cli.cli ask 'Analyze this code' --file demo.py")
        print("   uv run python -m ai_coding_cli.cli ask 'Generate tests' --preset test --file demo.py")
        print("\n3. Explore more features:")
        print("   uv run python -m ai_coding_cli.cli preset create my_preset 'Custom prompt ${variable}'")
        print("   uv run python -m ai_coding_cli.cli config set openai_model gpt-4")
        
    else:
        print("❌ Some commands failed. Please check the error messages above.")
    
    print(f"\n📚 For more information, see README.md")


if __name__ == "__main__":
    main()
