# AI Coding CLI

一个基于自然语言的AI编程助手命令行工具，使用OpenAI的function calling功能来帮助开发者完成各种编程任务。

## 特性

- 🤖 **自然语言交互**: 用自然语言描述你想要做的事情，无需记忆复杂的命令
- 🔧 **Function Calling**: 基于OpenAI的function calling技术，智能调用相应的工具
- 📁 **文件操作**: 读取、写入、创建、删除文件和目录
- 🔍 **代码分析**: 语法检查、导入分析、函数提取、复杂度分析等
- 📋 **预设指令**: 保存常用的提示词模板，支持变量替换
- 🛡️ **安全模式**: 在执行破坏性操作前会询问确认
- 🎨 **美观输出**: 使用Rich库提供彩色和格式化的输出
- ⚙️ **可配置**: 灵活的配置系统，支持环境变量和配置文件

## 安装

### 前置要求

- Python 3.10+
- OpenAI API密钥

### 使用uv安装（推荐）

```bash
# 克隆仓库
git clone <repository-url>
cd ai-coding-cli

# 创建虚拟环境
uv venv .venv --python=3.10

# 安装依赖
uv add openai click pydantic rich typer python-dotenv

# 安装开发依赖（可选）
uv add --dev pytest pytest-cov black isort flake8 mypy
```

### 配置

1. 设置OpenAI API密钥：

```bash
export OPENAI_API_KEY=your_openai_api_key_here
```

或者创建`.env`文件：

```bash
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
```

2. 运行基础测试验证安装：

```bash
uv run python test_basic.py
```

## 使用方法

### 基本命令

```bash
# 询问AI助手
uv run python -m ai_coding_cli.cli ask "创建一个计算斐波那契数列的Python函数"

# 使用文件上下文
uv run python -m ai_coding_cli.cli ask "重构这个类以使用依赖注入" --file myclass.py

# 使用预设指令
uv run python -m ai_coding_cli.cli ask "为这些文件生成单元测试" --preset test --file *.py

# 干运行模式（只显示会做什么，不实际执行）
uv run python -m ai_coding_cli.cli ask "删除所有临时文件" --dry-run

# 非交互模式
uv run python -m ai_coding_cli.cli ask "分析代码质量" --no-interactive
```

### 预设指令管理

```bash
# 列出所有预设
uv run python -m ai_coding_cli.cli preset list

# 查看预设内容
uv run python -m ai_coding_cli.cli preset show test

# 创建新预设
uv run python -m ai_coding_cli.cli preset create my_preset "自定义提示词模板 ${variable}"

# 删除预设
uv run python -m ai_coding_cli.cli preset delete my_preset
```

### 配置管理

```bash
# 查看当前配置
uv run python -m ai_coding_cli.cli config show

# 设置配置项
uv run python -m ai_coding_cli.cli config set openai_model gpt-4

# 重置配置
uv run python -m ai_coding_cli.cli config reset
```

## 使用示例

### 1. 创建新代码

```bash
uv run python -m ai_coding_cli.cli ask "创建一个Python类来管理用户账户，包括注册、登录和密码重置功能"
```

### 2. 代码重构

```bash
uv run python -m ai_coding_cli.cli ask "重构这个函数以提高可读性和性能" --file legacy_code.py
```

### 3. 生成测试

```bash
uv run python -m ai_coding_cli.cli ask "为这个模块生成全面的单元测试" --preset test --file calculator.py
```

### 4. 代码分析

```bash
uv run python -m ai_coding_cli.cli ask "分析这个项目的代码质量和潜在问题" --file "src/**/*.py"
```

### 5. 文档生成

```bash
uv run python -m ai_coding_cli.cli ask "为这些函数添加详细的文档字符串" --preset document --file utils.py
```

## 内置预设指令

工具包含以下预设指令：

- `test`: 生成单元测试
- `refactor`: 重构代码
- `document`: 添加文档
- `debug`: 调试和修复问题
- `optimize`: 性能优化
- `security`: 安全审计
- `convert`: 代码格式转换
- `explain`: 解释代码功能
- `api`: 生成API相关代码
- `migrate`: 代码迁移

## 可用工具

### 文件操作工具

- `read`: 读取文件内容
- `write`: 写入文件内容
- `create`: 创建新文件
- `delete`: 删除文件或目录
- `copy`: 复制文件或目录
- `move`: 移动/重命名文件
- `list`: 列出目录内容
- `mkdir`: 创建目录
- `exists`: 检查文件是否存在
- `info`: 获取文件信息

### 代码分析工具

- `syntax`: 语法检查
- `imports`: 导入分析
- `functions`: 函数提取
- `classes`: 类分析
- `complexity`: 复杂度分析
- `metrics`: 代码指标
- `dependencies`: 依赖分析
- `structure`: 结构分析
- `quality`: 质量评估
- `security`: 安全检查

## 配置选项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `openai_api_key` | - | OpenAI API密钥 |
| `openai_model` | gpt-4o-mini | 使用的OpenAI模型 |
| `openai_max_tokens` | 4096 | 最大token数 |
| `openai_temperature` | 0.1 | 温度参数 |
| `default_editor` | code | 默认编辑器 |
| `max_file_size` | 1048576 | 最大文件大小（字节） |
| `enable_logging` | true | 启用日志 |
| `log_level` | INFO | 日志级别 |
| `enable_file_operations` | true | 启用文件操作 |
| `enable_code_analysis` | true | 启用代码分析 |
| `safe_mode` | true | 安全模式 |

## 开发

### 运行测试

```bash
# 基础功能测试
uv run python test_basic.py

# 完整测试套件（需要先安装pytest）
uv run pytest

# 代码覆盖率
uv run pytest --cov=src/ai_coding_cli
```

### 代码格式化

```bash
# 格式化代码
uv run black src/ tests/

# 排序导入
uv run isort src/ tests/

# 代码检查
uv run flake8 src/ tests/

# 类型检查
uv run mypy src/
```

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License