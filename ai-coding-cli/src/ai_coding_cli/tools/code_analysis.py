"""Code analysis tool for AI Coding CLI.

This module provides code analysis capabilities including syntax checking,
dependency analysis, complexity metrics, and code quality assessment.
"""

import ast
import re
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from ..utils import (
    display_error,
    display_info,
    get_file_language,
    safe_read_file
)
from .base import BaseTool, ToolResult


class CodeAnalysisTool(BaseTool):
    """Tool for analyzing code structure, quality, and dependencies.
    
    This tool provides various code analysis capabilities to help
    understand and improve code quality.
    """
    
    @property
    def name(self) -> str:
        """Get the tool name."""
        return "code_analysis"
    
    @property
    def description(self) -> str:
        """Get the tool description."""
        return """Analyze code for various metrics and properties.
        
        Available analyses:
        - syntax: Check syntax validity
        - imports: Extract import statements and dependencies
        - functions: List all functions and methods
        - classes: List all classes and their methods
        - complexity: Calculate cyclomatic complexity
        - metrics: Get code metrics (lines, comments, etc.)
        - dependencies: Analyze project dependencies
        - structure: Analyze code structure and organization
        - quality: Assess code quality issues
        - security: Basic security issue detection"""
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Get the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "analysis_type": {
                    "type": "string",
                    "enum": [
                        "syntax", "imports", "functions", "classes",
                        "complexity", "metrics", "dependencies", 
                        "structure", "quality", "security"
                    ],
                    "description": "Type of analysis to perform"
                },
                "path": {
                    "type": "string",
                    "description": "File or directory path to analyze"
                },
                "recursive": {
                    "type": "boolean",
                    "default": False,
                    "description": "Analyze directories recursively"
                },
                "language": {
                    "type": "string",
                    "description": "Programming language (auto-detected if not specified)"
                },
                "include_patterns": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "File patterns to include (e.g., ['*.py', '*.js'])"
                },
                "exclude_patterns": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "File patterns to exclude"
                }
            },
            "required": ["analysis_type", "path"]
        }
    
    def execute(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool = True
    ) -> ToolResult:
        """Execute code analysis."""
        analysis_type = parameters.get("analysis_type")
        path = parameters.get("path")
        
        if not analysis_type or not path:
            return ToolResult(
                success=False,
                message="Missing required parameters",
                error="analysis_type and path are required"
            )
        
        try:
            # Dispatch to appropriate analysis method
            method_name = f"_analyze_{analysis_type}"
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                return method(parameters)
            else:
                return ToolResult(
                    success=False,
                    message=f"Unknown analysis type: {analysis_type}",
                    error=f"Analysis '{analysis_type}' not supported"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Code analysis failed: {e}",
                error=str(e)
            )
    
    def _get_files_to_analyze(self, parameters: Dict[str, Any]) -> List[Path]:
        """Get list of files to analyze based on parameters."""
        path = Path(parameters["path"])
        recursive = parameters.get("recursive", False)
        include_patterns = parameters.get("include_patterns", [])
        exclude_patterns = parameters.get("exclude_patterns", [])
        
        files = []
        
        if path.is_file():
            files = [path]
        elif path.is_dir():
            if recursive:
                files = list(path.rglob("*"))
            else:
                files = list(path.iterdir())
            
            # Filter to only files
            files = [f for f in files if f.is_file()]
            
            # Apply include patterns
            if include_patterns:
                filtered_files = []
                for pattern in include_patterns:
                    filtered_files.extend(path.glob(pattern))
                files = filtered_files
            
            # Apply exclude patterns
            if exclude_patterns:
                for pattern in exclude_patterns:
                    exclude_files = set(path.glob(pattern))
                    files = [f for f in files if f not in exclude_files]
        
        return files
    
    def _analyze_syntax(self, parameters: Dict[str, Any]) -> ToolResult:
        """Check syntax validity of code files."""
        files = self._get_files_to_analyze(parameters)
        results = []
        
        for file_path in files:
            language = get_file_language(str(file_path))
            content = safe_read_file(str(file_path))
            
            if content is None:
                continue
            
            syntax_result = {
                "file": str(file_path),
                "language": language,
                "valid": False,
                "errors": []
            }
            
            if language == "python":
                try:
                    ast.parse(content)
                    syntax_result["valid"] = True
                except SyntaxError as e:
                    syntax_result["errors"].append({
                        "line": e.lineno,
                        "column": e.offset,
                        "message": e.msg
                    })
            else:
                # For other languages, we'd need specific parsers
                syntax_result["valid"] = True
                syntax_result["message"] = f"Syntax checking not implemented for {language}"
            
            results.append(syntax_result)
        
        valid_count = sum(1 for r in results if r["valid"])
        total_count = len(results)
        
        return ToolResult(
            success=True,
            message=f"Syntax check complete: {valid_count}/{total_count} files valid",
            data={"results": results, "summary": {"valid": valid_count, "total": total_count}}
        )
    
    def _analyze_imports(self, parameters: Dict[str, Any]) -> ToolResult:
        """Extract import statements and dependencies."""
        files = self._get_files_to_analyze(parameters)
        results = []
        all_imports = set()
        
        for file_path in files:
            language = get_file_language(str(file_path))
            content = safe_read_file(str(file_path))
            
            if content is None:
                continue
            
            imports = []
            
            if language == "python":
                imports = self._extract_python_imports(content)
            elif language in ["javascript", "typescript"]:
                imports = self._extract_js_imports(content)
            
            file_result = {
                "file": str(file_path),
                "language": language,
                "imports": imports
            }
            
            results.append(file_result)
            all_imports.update(imports)
        
        return ToolResult(
            success=True,
            message=f"Import analysis complete: {len(all_imports)} unique imports found",
            data={
                "results": results,
                "all_imports": sorted(list(all_imports)),
                "summary": {"total_imports": len(all_imports), "files_analyzed": len(results)}
            }
        )
    
    def _extract_python_imports(self, content: str) -> List[str]:
        """Extract Python import statements."""
        imports = []
        
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
        except SyntaxError:
            # Fallback to regex if AST parsing fails
            import_patterns = [
                r'^\s*import\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)',
                r'^\s*from\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\s+import'
            ]
            
            for line in content.split('\n'):
                for pattern in import_patterns:
                    match = re.match(pattern, line)
                    if match:
                        imports.append(match.group(1))
        
        return imports
    
    def _extract_js_imports(self, content: str) -> List[str]:
        """Extract JavaScript/TypeScript import statements."""
        imports = []
        
        # Regex patterns for different import styles
        patterns = [
            r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',
            r'import\s+[\'"]([^\'"]+)[\'"]',
            r'require\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)',
        ]
        
        for line in content.split('\n'):
            for pattern in patterns:
                matches = re.findall(pattern, line)
                imports.extend(matches)
        
        return imports
    
    def _analyze_functions(self, parameters: Dict[str, Any]) -> ToolResult:
        """List all functions and methods."""
        files = self._get_files_to_analyze(parameters)
        results = []
        
        for file_path in files:
            language = get_file_language(str(file_path))
            content = safe_read_file(str(file_path))
            
            if content is None:
                continue
            
            functions = []
            
            if language == "python":
                functions = self._extract_python_functions(content)
            
            file_result = {
                "file": str(file_path),
                "language": language,
                "functions": functions
            }
            
            results.append(file_result)
        
        total_functions = sum(len(r["functions"]) for r in results)
        
        return ToolResult(
            success=True,
            message=f"Function analysis complete: {total_functions} functions found",
            data={
                "results": results,
                "summary": {"total_functions": total_functions, "files_analyzed": len(results)}
            }
        )
    
    def _extract_python_functions(self, content: str) -> List[Dict[str, Any]]:
        """Extract Python function definitions."""
        functions = []
        
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        "name": node.name,
                        "line": node.lineno,
                        "args": [arg.arg for arg in node.args.args],
                        "is_async": isinstance(node, ast.AsyncFunctionDef),
                        "decorators": [ast.unparse(d) for d in node.decorator_list] if hasattr(ast, 'unparse') else []
                    }
                    functions.append(func_info)
        except SyntaxError:
            # Fallback to regex
            pattern = r'^\s*(?:async\s+)?def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
            for i, line in enumerate(content.split('\n'), 1):
                match = re.match(pattern, line)
                if match:
                    functions.append({
                        "name": match.group(1),
                        "line": i,
                        "args": [],
                        "is_async": "async" in line,
                        "decorators": []
                    })
        
        return functions
