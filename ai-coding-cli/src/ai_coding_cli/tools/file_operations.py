"""File operations tool for AI Coding CLI.

This module provides file reading, writing, and manipulation capabilities
for the AI assistant.
"""

import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional

from ..utils import (
    display_code, 
    display_error, 
    display_info,
    get_file_language,
    safe_read_file,
    safe_write_file
)
from .base import BaseTool, ToolResult


class FileOperationsTool(BaseTool):
    """Tool for file operations including read, write, create, delete, etc.
    
    This tool provides comprehensive file manipulation capabilities
    for the AI assistant to work with code files.
    """
    
    @property
    def name(self) -> str:
        """Get the tool name."""
        return "file_operations"
    
    @property
    def description(self) -> str:
        """Get the tool description."""
        return """Perform file operations including reading, writing, creating, deleting, and listing files and directories. 
        
        Available operations:
        - read: Read file content
        - write: Write content to file (creates or overwrites)
        - append: Append content to file
        - create: Create new file with content
        - delete: Delete file or directory
        - list: List directory contents
        - copy: Copy file or directory
        - move: Move/rename file or directory
        - mkdir: Create directory
        - exists: Check if file/directory exists
        - info: Get file/directory information"""
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Get the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "operation": {
                    "type": "string",
                    "enum": [
                        "read", "write", "append", "create", "delete",
                        "list", "copy", "move", "mkdir", "exists", "info"
                    ],
                    "description": "File operation to perform"
                },
                "path": {
                    "type": "string",
                    "description": "File or directory path"
                },
                "content": {
                    "type": "string",
                    "description": "Content to write (for write/append/create operations)"
                },
                "destination": {
                    "type": "string",
                    "description": "Destination path (for copy/move operations)"
                },
                "recursive": {
                    "type": "boolean",
                    "default": False,
                    "description": "Recursive operation for directories"
                },
                "backup": {
                    "type": "boolean",
                    "default": True,
                    "description": "Create backup before overwriting files"
                },
                "encoding": {
                    "type": "string",
                    "default": "utf-8",
                    "description": "File encoding"
                }
            },
            "required": ["operation", "path"]
        }
    
    def execute(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool = True
    ) -> ToolResult:
        """Execute file operation."""
        operation = parameters.get("operation")
        path = parameters.get("path")
        
        if not operation or not path:
            return ToolResult(
                success=False,
                message="Missing required parameters",
                error="operation and path are required"
            )
        
        try:
            # Dispatch to appropriate operation method
            method_name = f"_operation_{operation}"
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                return method(parameters, interactive)
            else:
                return ToolResult(
                    success=False,
                    message=f"Unknown operation: {operation}",
                    error=f"Operation '{operation}' not supported"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                message=f"File operation failed: {e}",
                error=str(e)
            )
    
    def is_destructive(self) -> bool:
        """Check if this tool performs destructive operations."""
        return True
    
    def requires_confirmation(self, parameters: Dict[str, Any]) -> bool:
        """Check if operation requires confirmation."""
        destructive_ops = ["write", "delete", "move", "copy"]
        operation = parameters.get("operation")
        return (
            self.config.safe_mode and 
            operation in destructive_ops
        )
    
    def get_confirmation_message(self, parameters: Dict[str, Any]) -> str:
        """Get confirmation message for operation."""
        operation = parameters.get("operation")
        path = parameters.get("path")
        
        if operation == "delete":
            return f"Delete {path}?"
        elif operation == "write":
            return f"Overwrite {path}?"
        elif operation in ["copy", "move"]:
            dest = parameters.get("destination", "")
            return f"{operation.title()} {path} to {dest}?"
        else:
            return f"Execute {operation} on {path}?"
    
    def _operation_read(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool
    ) -> ToolResult:
        """Read file content."""
        path = parameters["path"]
        encoding = parameters.get("encoding", "utf-8")
        
        content = safe_read_file(path, self.config.max_file_size, encoding)
        
        if content is None:
            return ToolResult(
                success=False,
                message=f"Failed to read file: {path}",
                error="File read failed"
            )
        
        # Display content with syntax highlighting
        language = get_file_language(path)
        display_code(content, language, f"File: {path}")
        
        return ToolResult(
            success=True,
            message=f"Successfully read file: {path}",
            data={"content": content, "encoding": encoding}
        )
    
    def _operation_write(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool
    ) -> ToolResult:
        """Write content to file."""
        path = parameters["path"]
        content = parameters.get("content", "")
        encoding = parameters.get("encoding", "utf-8")
        backup = parameters.get("backup", True)
        
        success = safe_write_file(path, content, encoding, backup)
        
        if success:
            return ToolResult(
                success=True,
                message=f"Successfully wrote file: {path}",
                modified_files=[path]
            )
        else:
            return ToolResult(
                success=False,
                message=f"Failed to write file: {path}",
                error="File write failed"
            )
    
    def _operation_append(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool
    ) -> ToolResult:
        """Append content to file."""
        path = parameters["path"]
        content = parameters.get("content", "")
        encoding = parameters.get("encoding", "utf-8")
        
        try:
            file_path = Path(path)
            
            # Create parent directories if needed
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Append content
            with open(file_path, 'a', encoding=encoding) as f:
                f.write(content)
            
            display_info(f"Content appended to: {path}")
            
            return ToolResult(
                success=True,
                message=f"Successfully appended to file: {path}",
                modified_files=[path]
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to append to file: {path}",
                error=str(e)
            )
    
    def _operation_create(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool
    ) -> ToolResult:
        """Create new file with content."""
        path = parameters["path"]
        content = parameters.get("content", "")
        encoding = parameters.get("encoding", "utf-8")
        
        file_path = Path(path)
        
        if file_path.exists():
            return ToolResult(
                success=False,
                message=f"File already exists: {path}",
                error="File already exists"
            )
        
        success = safe_write_file(path, content, encoding, backup=False)
        
        if success:
            return ToolResult(
                success=True,
                message=f"Successfully created file: {path}",
                created_files=[path]
            )
        else:
            return ToolResult(
                success=False,
                message=f"Failed to create file: {path}",
                error="File creation failed"
            )
    
    def _operation_delete(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool
    ) -> ToolResult:
        """Delete file or directory."""
        path = parameters["path"]
        recursive = parameters.get("recursive", False)
        
        try:
            file_path = Path(path)
            
            if not file_path.exists():
                return ToolResult(
                    success=False,
                    message=f"Path does not exist: {path}",
                    error="Path not found"
                )
            
            if file_path.is_file():
                file_path.unlink()
                display_info(f"Deleted file: {path}")
                return ToolResult(
                    success=True,
                    message=f"Successfully deleted file: {path}",
                    deleted_files=[path]
                )
            elif file_path.is_dir():
                if recursive:
                    shutil.rmtree(file_path)
                    display_info(f"Deleted directory recursively: {path}")
                else:
                    file_path.rmdir()
                    display_info(f"Deleted empty directory: {path}")
                
                return ToolResult(
                    success=True,
                    message=f"Successfully deleted directory: {path}",
                    deleted_files=[path]
                )
            
        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to delete: {path}",
                error=str(e)
            )

    def _operation_list(
        self,
        parameters: Dict[str, Any],
        interactive: bool
    ) -> ToolResult:
        """List directory contents."""
        path = parameters["path"]

        try:
            dir_path = Path(path)

            if not dir_path.exists():
                return ToolResult(
                    success=False,
                    message=f"Directory does not exist: {path}",
                    error="Directory not found"
                )

            if not dir_path.is_dir():
                return ToolResult(
                    success=False,
                    message=f"Path is not a directory: {path}",
                    error="Not a directory"
                )

            # List contents
            contents = []
            for item in dir_path.iterdir():
                item_info = {
                    "name": item.name,
                    "path": str(item),
                    "type": "directory" if item.is_dir() else "file",
                    "size": item.stat().st_size if item.is_file() else None
                }
                contents.append(item_info)

            # Sort by type then name
            contents.sort(key=lambda x: (x["type"], x["name"]))

            display_info(f"Directory listing for: {path}")
            for item in contents:
                type_icon = "📁" if item["type"] == "directory" else "📄"
                size_info = f" ({item['size']} bytes)" if item["size"] is not None else ""
                print(f"  {type_icon} {item['name']}{size_info}")

            return ToolResult(
                success=True,
                message=f"Successfully listed directory: {path}",
                data={"contents": contents}
            )

        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to list directory: {path}",
                error=str(e)
            )

    def _operation_copy(
        self,
        parameters: Dict[str, Any],
        interactive: bool
    ) -> ToolResult:
        """Copy file or directory."""
        path = parameters["path"]
        destination = parameters.get("destination")
        recursive = parameters.get("recursive", False)

        if not destination:
            return ToolResult(
                success=False,
                message="Destination path is required for copy operation",
                error="Missing destination"
            )

        try:
            src_path = Path(path)
            dest_path = Path(destination)

            if not src_path.exists():
                return ToolResult(
                    success=False,
                    message=f"Source does not exist: {path}",
                    error="Source not found"
                )

            if src_path.is_file():
                # Copy file
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dest_path)
                display_info(f"Copied file: {path} -> {destination}")

                return ToolResult(
                    success=True,
                    message=f"Successfully copied file: {path} -> {destination}",
                    created_files=[destination]
                )
            elif src_path.is_dir() and recursive:
                # Copy directory recursively
                shutil.copytree(src_path, dest_path, dirs_exist_ok=True)
                display_info(f"Copied directory recursively: {path} -> {destination}")

                return ToolResult(
                    success=True,
                    message=f"Successfully copied directory: {path} -> {destination}",
                    created_files=[destination]
                )
            else:
                return ToolResult(
                    success=False,
                    message="Cannot copy directory without recursive=true",
                    error="Recursive flag required for directories"
                )

        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to copy: {path} -> {destination}",
                error=str(e)
            )

    def _operation_move(
        self,
        parameters: Dict[str, Any],
        interactive: bool
    ) -> ToolResult:
        """Move/rename file or directory."""
        path = parameters["path"]
        destination = parameters.get("destination")

        if not destination:
            return ToolResult(
                success=False,
                message="Destination path is required for move operation",
                error="Missing destination"
            )

        try:
            src_path = Path(path)
            dest_path = Path(destination)

            if not src_path.exists():
                return ToolResult(
                    success=False,
                    message=f"Source does not exist: {path}",
                    error="Source not found"
                )

            # Create parent directories if needed
            dest_path.parent.mkdir(parents=True, exist_ok=True)

            # Move/rename
            shutil.move(src_path, dest_path)
            display_info(f"Moved: {path} -> {destination}")

            return ToolResult(
                success=True,
                message=f"Successfully moved: {path} -> {destination}",
                created_files=[destination],
                deleted_files=[path]
            )

        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to move: {path} -> {destination}",
                error=str(e)
            )

    def _operation_mkdir(
        self,
        parameters: Dict[str, Any],
        interactive: bool
    ) -> ToolResult:
        """Create directory."""
        path = parameters["path"]

        try:
            dir_path = Path(path)
            dir_path.mkdir(parents=True, exist_ok=True)
            display_info(f"Created directory: {path}")

            return ToolResult(
                success=True,
                message=f"Successfully created directory: {path}",
                created_files=[path]
            )

        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to create directory: {path}",
                error=str(e)
            )

    def _operation_exists(
        self,
        parameters: Dict[str, Any],
        interactive: bool
    ) -> ToolResult:
        """Check if file or directory exists."""
        path = parameters["path"]

        try:
            file_path = Path(path)
            exists = file_path.exists()

            return ToolResult(
                success=True,
                message=f"Path {'exists' if exists else 'does not exist'}: {path}",
                data={"exists": exists}
            )

        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to check existence: {path}",
                error=str(e)
            )

    def _operation_info(
        self,
        parameters: Dict[str, Any],
        interactive: bool
    ) -> ToolResult:
        """Get file or directory information."""
        path = parameters["path"]

        try:
            file_path = Path(path)

            if not file_path.exists():
                return ToolResult(
                    success=False,
                    message=f"Path does not exist: {path}",
                    error="Path not found"
                )

            stat = file_path.stat()
            info = {
                "path": str(file_path.absolute()),
                "name": file_path.name,
                "type": "directory" if file_path.is_dir() else "file",
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "permissions": oct(stat.st_mode)[-3:],
            }

            if file_path.is_file():
                info["extension"] = file_path.suffix
                info["language"] = get_file_language(str(file_path))

            display_info(f"File info for: {path}")
            for key, value in info.items():
                print(f"  {key}: {value}")

            return ToolResult(
                success=True,
                message=f"Successfully got info for: {path}",
                data=info
            )

        except Exception as e:
            return ToolResult(
                success=False,
                message=f"Failed to get info: {path}",
                error=str(e)
            )
