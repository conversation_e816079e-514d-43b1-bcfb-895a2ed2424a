"""Tool registry for AI Coding CLI.

This module manages the registration and execution of tools that can be
called by the AI assistant through OpenAI's function calling feature.
"""

import logging
from typing import Any, Dict, List, Optional

from ..config import Config
from ..utils import confirm_action, display_error, display_info, display_warning
from .base import BaseTool, ToolResult
from .file_operations import FileOperationsTool
from .code_analysis import CodeAnalysisTool

logger = logging.getLogger(__name__)


class ToolRegistry:
    """Registry for managing and executing AI tools.
    
    This class handles tool registration, validation, and execution
    for the function calling system.
    """
    
    def __init__(self, config: Config):
        """Initialize the tool registry.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.tools: Dict[str, BaseTool] = {}
        
        # Register default tools
        self._register_default_tools()
    
    def _register_default_tools(self) -> None:
        """Register default tools based on configuration."""
        try:
            # File operations tool
            if self.config.enable_file_operations:
                self.register_tool(FileOperationsTool(self.config))
            
            # Code analysis tool
            if self.config.enable_code_analysis:
                self.register_tool(CodeAnalysisTool(self.config))
            
            logger.info(f"Registered {len(self.tools)} tools")
            
        except Exception as e:
            logger.error(f"Error registering default tools: {e}")
            display_error(f"Error registering tools: {e}")
    
    def register_tool(self, tool: BaseTool) -> None:
        """Register a tool.
        
        Args:
            tool: Tool instance to register
        """
        try:
            self.tools[tool.name] = tool
            logger.info(f"Registered tool: {tool.name}")
            
        except Exception as e:
            logger.error(f"Error registering tool {tool.name}: {e}")
            display_error(f"Error registering tool: {e}")
    
    def unregister_tool(self, tool_name: str) -> bool:
        """Unregister a tool.
        
        Args:
            tool_name: Name of tool to unregister
            
        Returns:
            True if successful, False otherwise
        """
        if tool_name in self.tools:
            del self.tools[tool_name]
            logger.info(f"Unregistered tool: {tool_name}")
            return True
        return False
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool instance or None if not found
        """
        return self.tools.get(tool_name)
    
    def list_tools(self) -> List[str]:
        """Get list of registered tool names.
        
        Returns:
            List of tool names
        """
        return list(self.tools.keys())
    
    def get_openai_tools(self) -> List[Dict[str, Any]]:
        """Get OpenAI function calling schemas for all tools.
        
        Returns:
            List of OpenAI function schemas
        """
        schemas = []
        for tool in self.tools.values():
            try:
                schema = tool.get_openai_function_schema()
                schemas.append(schema)
            except Exception as e:
                logger.error(f"Error getting schema for tool {tool.name}: {e}")
        
        return schemas
    
    def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        interactive: bool = True
    ) -> Dict[str, Any]:
        """Execute a tool with given parameters.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            interactive: Whether to ask for user confirmation
            
        Returns:
            Dictionary with execution results
        """
        try:
            # Get the tool
            tool = self.get_tool(tool_name)
            if not tool:
                return {
                    "success": False,
                    "error": f"Tool '{tool_name}' not found"
                }
            
            # Validate parameters
            if not tool.validate_parameters(parameters):
                return {
                    "success": False,
                    "error": f"Invalid parameters for tool '{tool_name}'"
                }
            
            # Check if confirmation is needed
            if interactive and tool.requires_confirmation(parameters):
                confirmation_msg = tool.get_confirmation_message(parameters)
                if not confirm_action(confirmation_msg):
                    return {
                        "success": False,
                        "message": "Operation cancelled by user"
                    }
            
            # Execute the tool
            display_info(f"Executing tool: {tool_name}")
            result = tool.execute(parameters, interactive=interactive)
            
            # Convert ToolResult to dictionary
            result_dict = result.dict()
            
            # Log the execution
            if result.success:
                logger.info(f"Tool {tool_name} executed successfully")
                if result.modified_files:
                    display_info(f"Modified files: {', '.join(result.modified_files)}")
                if result.created_files:
                    display_info(f"Created files: {', '.join(result.created_files)}")
                if result.deleted_files:
                    display_warning(f"Deleted files: {', '.join(result.deleted_files)}")
            else:
                logger.error(f"Tool {tool_name} execution failed: {result.error}")
                display_error(f"Tool execution failed: {result.error}")
            
            return result_dict
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool information dictionary or None if not found
        """
        tool = self.get_tool(tool_name)
        if not tool:
            return None
        
        return {
            "name": tool.name,
            "description": tool.description,
            "parameters": tool.parameters,
            "is_destructive": tool.is_destructive(),
            "schema": tool.get_openai_function_schema()
        }
    
    def validate_all_tools(self) -> bool:
        """Validate all registered tools.
        
        Returns:
            True if all tools are valid, False otherwise
        """
        all_valid = True
        
        for tool_name, tool in self.tools.items():
            try:
                # Check if tool has required methods
                if not hasattr(tool, 'execute'):
                    logger.error(f"Tool {tool_name} missing execute method")
                    all_valid = False
                    continue
                
                # Validate schema
                schema = tool.get_openai_function_schema()
                if not isinstance(schema, dict):
                    logger.error(f"Tool {tool_name} has invalid schema")
                    all_valid = False
                    continue
                
                logger.info(f"Tool {tool_name} validation passed")
                
            except Exception as e:
                logger.error(f"Tool {tool_name} validation failed: {e}")
                all_valid = False
        
        return all_valid
