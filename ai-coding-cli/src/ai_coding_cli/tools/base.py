"""Base classes for AI Coding CLI tools.

This module defines the base classes and interfaces that all tools must
implement to be compatible with the function calling system.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from pydantic import BaseModel


class ToolResult(BaseModel):
    """Result of a tool execution.
    
    Attributes:
        success: Whether the tool execution was successful
        message: Human-readable message about the result
        data: Optional data returned by the tool
        error: Error message if execution failed
        modified_files: List of files that were modified
        created_files: List of files that were created
        deleted_files: List of files that were deleted
    """
    success: bool
    message: str
    data: Optional[Any] = None
    error: Optional[str] = None
    modified_files: List[str] = []
    created_files: List[str] = []
    deleted_files: List[str] = []


class BaseTool(ABC):
    """Base class for all AI Coding CLI tools.
    
    All tools must inherit from this class and implement the required methods
    to be compatible with the function calling system.
    """
    
    def __init__(self, config: 'Config'):
        """Initialize the tool.
        
        Args:
            config: Configuration object
        """
        self.config = config
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get the tool name.
        
        Returns:
            Tool name for function calling
        """
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Get the tool description.
        
        Returns:
            Tool description for the AI model
        """
        pass
    
    @property
    @abstractmethod
    def parameters(self) -> Dict[str, Any]:
        """Get the tool parameters schema.
        
        Returns:
            JSON Schema for tool parameters
        """
        pass
    
    @abstractmethod
    def execute(
        self, 
        parameters: Dict[str, Any], 
        interactive: bool = True
    ) -> ToolResult:
        """Execute the tool with given parameters.
        
        Args:
            parameters: Tool parameters
            interactive: Whether to ask for user confirmation
            
        Returns:
            ToolResult object with execution results
        """
        pass
    
    def get_openai_function_schema(self) -> Dict[str, Any]:
        """Get OpenAI function calling schema for this tool.
        
        Returns:
            OpenAI function schema dictionary
        """
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """Validate tool parameters against schema.
        
        Args:
            parameters: Parameters to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Basic validation - can be enhanced with jsonschema
            required_params = self.parameters.get('required', [])
            for param in required_params:
                if param not in parameters:
                    return False
            return True
        except Exception:
            return False
    
    def is_destructive(self) -> bool:
        """Check if this tool performs destructive operations.
        
        Returns:
            True if tool can modify/delete files, False otherwise
        """
        return False
    
    def requires_confirmation(self, parameters: Dict[str, Any]) -> bool:
        """Check if this tool execution requires user confirmation.
        
        Args:
            parameters: Tool parameters
            
        Returns:
            True if confirmation is needed, False otherwise
        """
        return self.is_destructive() and self.config.safe_mode
    
    def get_confirmation_message(self, parameters: Dict[str, Any]) -> str:
        """Get confirmation message for this tool execution.
        
        Args:
            parameters: Tool parameters
            
        Returns:
            Confirmation message string
        """
        return f"Execute {self.name} with parameters: {parameters}?"
