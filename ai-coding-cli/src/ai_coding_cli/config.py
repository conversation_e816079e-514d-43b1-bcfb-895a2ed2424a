"""Configuration management for AI Coding CLI.

This module handles loading, saving, and managing configuration settings
including API keys, user preferences, and tool settings.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, validator
from rich.console import Console
from rich.table import Table

console = Console()


class Config(BaseModel):
    """Configuration model for the AI Coding CLI.
    
    This class defines all configuration options and their default values,
    handles validation, and provides methods for loading/saving configuration.
    """
    
    # OpenAI API Configuration
    openai_api_key: str = Field(
        default="",
        description="OpenAI API key for accessing GPT models"
    )
    openai_model: str = Field(
        default="gpt-4o-mini",
        description="OpenAI model to use for function calling"
    )
    openai_max_tokens: int = Field(
        default=4096,
        ge=1,
        le=32768,
        description="Maximum tokens for OpenAI API responses"
    )
    openai_temperature: float = Field(
        default=0.1,
        ge=0.0,
        le=2.0,
        description="Temperature for OpenAI API calls"
    )
    
    # CLI Configuration
    default_editor: str = Field(
        default="code",
        description="Default editor command for opening files"
    )
    max_file_size: int = Field(
        default=1048576,  # 1MB
        ge=1024,
        description="Maximum file size to read in bytes"
    )
    enable_logging: bool = Field(
        default=True,
        description="Enable logging to file"
    )
    log_level: str = Field(
        default="INFO",
        description="Logging level (DEBUG, INFO, WARNING, ERROR)"
    )
    
    # Tool Configuration
    enable_file_operations: bool = Field(
        default=True,
        description="Enable file read/write operations"
    )
    enable_code_analysis: bool = Field(
        default=True,
        description="Enable code analysis tools"
    )
    enable_refactoring: bool = Field(
        default=True,
        description="Enable code refactoring tools"
    )
    safe_mode: bool = Field(
        default=True,
        description="Enable safe mode (ask before destructive operations)"
    )
    
    # Internal paths
    config_dir: Path = Field(
        default_factory=lambda: Path.home() / ".ai-coding-cli",
        description="Configuration directory path"
    )
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level is one of the allowed values."""
        allowed_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        if v.upper() not in allowed_levels:
            raise ValueError(f'Log level must be one of: {allowed_levels}')
        return v.upper()
    
    @validator('openai_model')
    def validate_openai_model(cls, v):
        """Validate OpenAI model name."""
        allowed_models = [
            'gpt-4o-mini', 'gpt-4o', 'gpt-4-turbo', 'gpt-4',
            'gpt-3.5-turbo', 'gpt-3.5-turbo-16k'
        ]
        if v not in allowed_models:
            console.print(f"[yellow]Warning: Model '{v}' not in known models list[/yellow]")
        return v
    
    def save(self) -> None:
        """Save configuration to file."""
        config_file = self.config_dir / "config.json"
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # Don't save sensitive information like API keys to file
        # They should be in environment variables or .env file
        config_data = self.dict(exclude={'openai_api_key'})
        
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2, default=str)
    
    def load_from_file(self) -> None:
        """Load configuration from file."""
        config_file = self.config_dir / "config.json"
        if config_file.exists():
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            # Update current instance with loaded data
            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value and save to file."""
        if hasattr(self, key):
            # Convert string values to appropriate types
            field_info = self.model_fields[key]
            field_type = field_info.annotation
            
            if field_type == bool:
                value = str(value).lower() in ('true', '1', 'yes', 'on')
            elif field_type == int:
                value = int(value)
            elif field_type == float:
                value = float(value)
            
            setattr(self, key, value)
            self.save()
        else:
            raise ValueError(f"Unknown configuration key: {key}")
    
    def reset(self) -> None:
        """Reset configuration to defaults."""
        config_file = self.config_dir / "config.json"
        if config_file.exists():
            config_file.unlink()
        
        # Reset to defaults
        default_config = Config()
        for key, value in default_config.dict().items():
            if key != 'config_dir':  # Don't reset the config directory
                setattr(self, key, value)
    
    def display(self) -> None:
        """Display current configuration in a nice table."""
        table = Table(title="AI Coding CLI Configuration")
        table.add_column("Setting", style="cyan", no_wrap=True)
        table.add_column("Value", style="magenta")
        table.add_column("Description", style="green")
        
        for field_name, field_info in self.model_fields.items():
            if field_name == 'openai_api_key':
                # Don't display API key, show masked version
                value = "***" if self.openai_api_key else "Not set"
            else:
                value = str(getattr(self, field_name))

            table.add_row(
                field_name,
                value,
                field_info.description or ""
            )
        
        console.print(table)
    
    def validate_required(self) -> bool:
        """Validate that required configuration is present."""
        if not self.openai_api_key:
            console.print("[red]Error: OpenAI API key is required[/red]")
            console.print("Set it using: export OPENAI_API_KEY=your_key_here")
            return False
        return True


def load_config() -> Config:
    """Load configuration from environment and config file.
    
    Priority order:
    1. Environment variables
    2. .env file
    3. Config file
    4. Defaults
    """
    # Load environment variables from .env file if it exists
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        pass
    
    # Create config instance with defaults
    config = Config()
    
    # Load from config file if it exists
    config.load_from_file()
    
    # Override with environment variables
    env_mappings = {
        'OPENAI_API_KEY': 'openai_api_key',
        'OPENAI_MODEL': 'openai_model',
        'OPENAI_MAX_TOKENS': 'openai_max_tokens',
        'OPENAI_TEMPERATURE': 'openai_temperature',
        'DEFAULT_EDITOR': 'default_editor',
        'MAX_FILE_SIZE': 'max_file_size',
        'ENABLE_LOGGING': 'enable_logging',
        'LOG_LEVEL': 'log_level',
        'ENABLE_FILE_OPERATIONS': 'enable_file_operations',
        'ENABLE_CODE_ANALYSIS': 'enable_code_analysis',
        'ENABLE_REFACTORING': 'enable_refactoring',
        'SAFE_MODE': 'safe_mode',
    }
    
    for env_var, config_key in env_mappings.items():
        env_value = os.getenv(env_var)
        if env_value is not None:
            try:
                config.set(config_key, env_value)
            except (ValueError, TypeError) as e:
                console.print(f"[yellow]Warning: Invalid value for {env_var}: {e}[/yellow]")
    
    return config
