"""Utility functions for AI Coding CLI.

This module provides common utility functions used throughout the application
including logging setup, file operations, and display helpers.
"""

import logging
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.panel import Panel
from rich.syntax import Syntax
from rich.text import Text

console = Console()


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging with Rich handler for beautiful console output.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
    """
    # Create logs directory
    log_dir = Path.home() / ".ai-coding-cli" / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            RichHandler(console=console, show_time=False, show_path=False),
            logging.FileHandler(log_dir / "ai-coding-cli.log"),
        ]
    )
    
    # Reduce noise from external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)


def display_error(message: str, title: str = "Error") -> None:
    """Display an error message in a red panel.
    
    Args:
        message: Error message to display
        title: Panel title
    """
    console.print(Panel(
        Text(message, style="bold red"),
        title=title,
        border_style="red"
    ))


def display_success(message: str, title: str = "Success") -> None:
    """Display a success message in a green panel.
    
    Args:
        message: Success message to display
        title: Panel title
    """
    console.print(Panel(
        Text(message, style="bold green"),
        title=title,
        border_style="green"
    ))


def display_warning(message: str, title: str = "Warning") -> None:
    """Display a warning message in a yellow panel.
    
    Args:
        message: Warning message to display
        title: Panel title
    """
    console.print(Panel(
        Text(message, style="bold yellow"),
        title=title,
        border_style="yellow"
    ))


def display_info(message: str, title: str = "Info") -> None:
    """Display an info message in a blue panel.
    
    Args:
        message: Info message to display
        title: Panel title
    """
    console.print(Panel(
        Text(message, style="bold blue"),
        title=title,
        border_style="blue"
    ))


def display_code(
    code: str, 
    language: str = "python", 
    title: Optional[str] = None,
    line_numbers: bool = True
) -> None:
    """Display code with syntax highlighting.
    
    Args:
        code: Code content to display
        language: Programming language for syntax highlighting
        title: Optional title for the code block
        line_numbers: Whether to show line numbers
    """
    syntax = Syntax(
        code,
        language,
        theme="monokai",
        line_numbers=line_numbers,
        word_wrap=True
    )
    
    if title:
        console.print(Panel(syntax, title=title, border_style="blue"))
    else:
        console.print(syntax)


def confirm_action(message: str, default: bool = False) -> bool:
    """Ask user for confirmation.
    
    Args:
        message: Confirmation message
        default: Default value if user just presses Enter
        
    Returns:
        True if user confirms, False otherwise
    """
    default_text = "Y/n" if default else "y/N"
    response = console.input(f"[yellow]{message} ({default_text}): [/yellow]")
    
    if not response.strip():
        return default
    
    return response.lower().startswith('y')


def get_file_language(file_path: str) -> str:
    """Determine programming language from file extension.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Language name for syntax highlighting
    """
    extension_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.jsx': 'jsx',
        '.tsx': 'tsx',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.h': 'c',
        '.hpp': 'cpp',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby',
        '.go': 'go',
        '.rs': 'rust',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.sh': 'bash',
        '.bash': 'bash',
        '.zsh': 'zsh',
        '.fish': 'fish',
        '.ps1': 'powershell',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.sass': 'sass',
        '.less': 'less',
        '.xml': 'xml',
        '.json': 'json',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.toml': 'toml',
        '.ini': 'ini',
        '.cfg': 'ini',
        '.conf': 'ini',
        '.md': 'markdown',
        '.rst': 'rst',
        '.tex': 'latex',
        '.sql': 'sql',
        '.r': 'r',
        '.R': 'r',
        '.m': 'matlab',
        '.pl': 'perl',
        '.lua': 'lua',
        '.vim': 'vim',
        '.dockerfile': 'dockerfile',
        '.makefile': 'makefile',
    }
    
    path = Path(file_path)
    extension = path.suffix.lower()
    
    # Special cases for files without extensions
    if not extension:
        name = path.name.lower()
        if name in ['dockerfile', 'makefile', 'rakefile']:
            return name
        elif name.startswith('.'):
            return 'bash'  # Config files
    
    return extension_map.get(extension, 'text')


def safe_read_file(
    file_path: str, 
    max_size: int = 1048576,  # 1MB
    encoding: str = 'utf-8'
) -> Optional[str]:
    """Safely read a file with size and encoding checks.
    
    Args:
        file_path: Path to the file to read
        max_size: Maximum file size in bytes
        encoding: File encoding
        
    Returns:
        File content as string, or None if reading failed
    """
    try:
        path = Path(file_path)
        
        if not path.exists():
            display_error(f"File not found: {file_path}")
            return None
        
        if not path.is_file():
            display_error(f"Not a file: {file_path}")
            return None
        
        # Check file size
        file_size = path.stat().st_size
        if file_size > max_size:
            display_error(
                f"File too large: {file_path} ({file_size} bytes > {max_size} bytes)"
            )
            return None
        
        # Try to read with specified encoding
        try:
            return path.read_text(encoding=encoding)
        except UnicodeDecodeError:
            # Try with different encodings
            for fallback_encoding in ['latin-1', 'cp1252', 'utf-16']:
                try:
                    content = path.read_text(encoding=fallback_encoding)
                    display_warning(
                        f"File {file_path} read with {fallback_encoding} encoding"
                    )
                    return content
                except UnicodeDecodeError:
                    continue
            
            display_error(f"Could not decode file: {file_path}")
            return None
            
    except Exception as e:
        display_error(f"Error reading file {file_path}: {e}")
        return None


def safe_write_file(
    file_path: str,
    content: str,
    encoding: str = 'utf-8',
    backup: bool = True
) -> bool:
    """Safely write content to a file with optional backup.
    
    Args:
        file_path: Path to the file to write
        content: Content to write
        encoding: File encoding
        backup: Whether to create a backup of existing file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        path = Path(file_path)
        
        # Create backup if file exists and backup is requested
        if backup and path.exists():
            backup_path = path.with_suffix(path.suffix + '.bak')
            path.rename(backup_path)
            display_info(f"Backup created: {backup_path}")
        
        # Create parent directories if they don't exist
        path.parent.mkdir(parents=True, exist_ok=True)
        
        # Write content
        path.write_text(content, encoding=encoding)
        display_success(f"File written: {file_path}")
        return True
        
    except Exception as e:
        display_error(f"Error writing file {file_path}: {e}")
        return False


def format_file_size(size_bytes: int) -> str:
    """Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to specified length with ellipsis.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        
    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - 3] + "..."
