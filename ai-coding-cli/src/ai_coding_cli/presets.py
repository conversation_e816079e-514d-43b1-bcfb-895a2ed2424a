"""Preset management for AI Coding CLI.

This module handles creating, storing, editing, and using preset commands
that allow users to save frequently used prompts with template variables.
"""

import json
from pathlib import Path
from typing import Any, Dict, List, Optional
from string import Template

from .utils import display_error, display_success, display_warning


class PresetManager:
    """Manages preset commands for the AI Coding CLI.
    
    Presets are stored as JSON files and support template variable substitution
    using Python's string.Template syntax (e.g., ${variable_name}).
    """
    
    def __init__(self, config_dir: Path):
        """Initialize the preset manager.
        
        Args:
            config_dir: Configuration directory path
        """
        self.config_dir = Path(config_dir)
        self.presets_dir = self.config_dir / "presets"
        self.presets_file = self.config_dir / "user_presets.json"
        
        # Create directories if they don't exist
        self.presets_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize with default presets if file doesn't exist
        if not self.presets_file.exists():
            self._create_default_presets()
    
    def _create_default_presets(self) -> None:
        """Create default preset commands."""
        default_presets = {
            "test": {
                "template": "Generate comprehensive unit tests for the following code. Include edge cases, error handling, and mock dependencies where appropriate. Files: ${files}",
                "description": "Generate unit tests for specified files",
                "variables": ["files"]
            },
            "refactor": {
                "template": "Refactor the following code to improve readability, maintainability, and performance. Follow best practices and design patterns. Preserve existing functionality. ${prompt} Files: ${files}",
                "description": "Refactor code with specific improvements",
                "variables": ["prompt", "files"]
            },
            "document": {
                "template": "Add comprehensive documentation to the following code including docstrings, type hints, and inline comments. Follow the language's documentation conventions. Files: ${files}",
                "description": "Add documentation to code files",
                "variables": ["files"]
            },
            "debug": {
                "template": "Analyze the following code for potential bugs, errors, and issues. Provide specific fixes and explanations. ${prompt} Files: ${files}",
                "description": "Debug and fix issues in code",
                "variables": ["prompt", "files"]
            },
            "optimize": {
                "template": "Optimize the following code for better performance, memory usage, and efficiency. Explain the optimizations made. ${prompt} Files: ${files}",
                "description": "Optimize code performance",
                "variables": ["prompt", "files"]
            },
            "security": {
                "template": "Perform a security audit of the following code. Identify vulnerabilities, security issues, and provide fixes. Focus on: ${prompt} Files: ${files}",
                "description": "Security audit and fixes",
                "variables": ["prompt", "files"]
            },
            "convert": {
                "template": "Convert the following code from one format/language to another. ${prompt} Files: ${files}",
                "description": "Convert code between languages/formats",
                "variables": ["prompt", "files"]
            },
            "explain": {
                "template": "Explain how the following code works in detail. Break down complex logic, algorithms, and design patterns used. ${prompt} Files: ${files}",
                "description": "Explain code functionality",
                "variables": ["prompt", "files"]
            },
            "api": {
                "template": "Generate API documentation, endpoints, or client code based on the following specifications. ${prompt} Files: ${files}",
                "description": "Generate API-related code",
                "variables": ["prompt", "files"]
            },
            "migrate": {
                "template": "Help migrate the following code to a newer version, framework, or library. ${prompt} Files: ${files}",
                "description": "Migrate code to newer versions",
                "variables": ["prompt", "files"]
            }
        }
        
        self._save_presets(default_presets)
        display_success("Default presets created")
    
    def _load_presets(self) -> Dict[str, Dict[str, Any]]:
        """Load presets from file.
        
        Returns:
            Dictionary of preset name to preset data
        """
        try:
            if self.presets_file.exists():
                with open(self.presets_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            display_error(f"Error loading presets: {e}")
            return {}
    
    def _save_presets(self, presets: Dict[str, Dict[str, Any]]) -> None:
        """Save presets to file.
        
        Args:
            presets: Dictionary of preset data to save
        """
        try:
            with open(self.presets_file, 'w', encoding='utf-8') as f:
                json.dump(presets, f, indent=2, ensure_ascii=False)
        except Exception as e:
            display_error(f"Error saving presets: {e}")
    
    def list_presets(self) -> List[str]:
        """Get list of available preset names.
        
        Returns:
            List of preset names
        """
        presets = self._load_presets()
        return sorted(presets.keys())
    
    def get_preset(self, name: str) -> Optional[str]:
        """Get preset template by name.
        
        Args:
            name: Preset name
            
        Returns:
            Preset template string, or None if not found
        """
        presets = self._load_presets()
        preset_data = presets.get(name)
        if preset_data:
            return preset_data.get('template')
        return None
    
    def get_preset_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get full preset information by name.
        
        Args:
            name: Preset name
            
        Returns:
            Preset data dictionary, or None if not found
        """
        presets = self._load_presets()
        return presets.get(name)
    
    def create_preset(
        self, 
        name: str, 
        template: str, 
        description: str = "",
        variables: Optional[List[str]] = None
    ) -> bool:
        """Create a new preset.
        
        Args:
            name: Preset name
            template: Template string with variables
            description: Optional description
            variables: List of variable names used in template
            
        Returns:
            True if successful, False otherwise
        """
        try:
            presets = self._load_presets()
            
            # Extract variables from template if not provided
            if variables is None:
                variables = self._extract_variables(template)
            
            presets[name] = {
                'template': template,
                'description': description,
                'variables': variables,
                'created_at': self._get_timestamp()
            }
            
            self._save_presets(presets)
            return True
            
        except Exception as e:
            display_error(f"Error creating preset: {e}")
            return False
    
    def update_preset(
        self, 
        name: str, 
        template: Optional[str] = None,
        description: Optional[str] = None,
        variables: Optional[List[str]] = None
    ) -> bool:
        """Update an existing preset.
        
        Args:
            name: Preset name
            template: New template string
            description: New description
            variables: New variable list
            
        Returns:
            True if successful, False otherwise
        """
        try:
            presets = self._load_presets()
            
            if name not in presets:
                display_error(f"Preset '{name}' not found")
                return False
            
            preset_data = presets[name]
            
            if template is not None:
                preset_data['template'] = template
                preset_data['variables'] = variables or self._extract_variables(template)
            
            if description is not None:
                preset_data['description'] = description
            
            preset_data['updated_at'] = self._get_timestamp()
            
            self._save_presets(presets)
            return True
            
        except Exception as e:
            display_error(f"Error updating preset: {e}")
            return False
    
    def delete_preset(self, name: str) -> bool:
        """Delete a preset.
        
        Args:
            name: Preset name
            
        Returns:
            True if successful, False otherwise
        """
        try:
            presets = self._load_presets()
            
            if name not in presets:
                return False
            
            del presets[name]
            self._save_presets(presets)
            return True
            
        except Exception as e:
            display_error(f"Error deleting preset: {e}")
            return False
    
    def render_preset(
        self, 
        template: str, 
        variables: Dict[str, Any]
    ) -> str:
        """Render a preset template with variables.
        
        Args:
            template: Template string
            variables: Dictionary of variable values
            
        Returns:
            Rendered template string
        """
        try:
            # Convert list variables to comma-separated strings
            processed_vars = {}
            for key, value in variables.items():
                if isinstance(value, list):
                    processed_vars[key] = ', '.join(str(v) for v in value)
                else:
                    processed_vars[key] = str(value)
            
            # Use safe_substitute to avoid KeyError for missing variables
            template_obj = Template(template)
            return template_obj.safe_substitute(processed_vars)
            
        except Exception as e:
            display_error(f"Error rendering preset template: {e}")
            return template
    
    def _extract_variables(self, template: str) -> List[str]:
        """Extract variable names from a template string.
        
        Args:
            template: Template string
            
        Returns:
            List of variable names found in template
        """
        import re
        
        # Find all ${variable_name} patterns
        pattern = r'\$\{([^}]+)\}'
        matches = re.findall(pattern, template)
        return sorted(set(matches))
    
    def _get_timestamp(self) -> str:
        """Get current timestamp as ISO string.
        
        Returns:
            ISO formatted timestamp string
        """
        from datetime import datetime
        return datetime.now().isoformat()
    
    def export_presets(self, file_path: str) -> bool:
        """Export presets to a file.
        
        Args:
            file_path: Path to export file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            presets = self._load_presets()
            export_path = Path(file_path)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(presets, f, indent=2, ensure_ascii=False)
            
            display_success(f"Presets exported to {file_path}")
            return True
            
        except Exception as e:
            display_error(f"Error exporting presets: {e}")
            return False
    
    def import_presets(self, file_path: str, overwrite: bool = False) -> bool:
        """Import presets from a file.
        
        Args:
            file_path: Path to import file
            overwrite: Whether to overwrite existing presets
            
        Returns:
            True if successful, False otherwise
        """
        try:
            import_path = Path(file_path)
            
            if not import_path.exists():
                display_error(f"Import file not found: {file_path}")
                return False
            
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_presets = json.load(f)
            
            current_presets = self._load_presets()
            
            conflicts = []
            for name in imported_presets:
                if name in current_presets and not overwrite:
                    conflicts.append(name)
            
            if conflicts:
                display_warning(
                    f"Conflicts found: {', '.join(conflicts)}. "
                    "Use overwrite=True to replace existing presets."
                )
                return False
            
            # Merge presets
            current_presets.update(imported_presets)
            self._save_presets(current_presets)
            
            display_success(f"Imported {len(imported_presets)} presets from {file_path}")
            return True
            
        except Exception as e:
            display_error(f"Error importing presets: {e}")
            return False
