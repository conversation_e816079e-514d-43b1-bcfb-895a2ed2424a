"""Main CLI interface for AI Coding CLI.

This module provides the command-line interface using Type<PERSON> for the AI coding
assistant tool. It handles user input, command parsing, and orchestrates
the interaction between different components.
"""

import os
import sys
from pathlib import Path
from typing import Optional, List

import typer
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from .config import Config, load_config
from .ai_client import AIClient
from .presets import PresetManager
from .tools.registry import ToolRegistry
from .utils import setup_logging, display_error, display_success

# Initialize Rich console for beautiful output
console = Console()

# Create the main Typer app
app = typer.Typer(
    name="ai-code",
    help="AI Coding CLI - Natural language AI coding assistant",
    add_completion=False,
    rich_markup_mode="rich",
)

# Global configuration
config: Optional[Config] = None
ai_client: Optional[AIClient] = None
preset_manager: Optional[PresetManager] = None
tool_registry: Optional[ToolRegistry] = None


def initialize_app() -> None:
    """Initialize the application with configuration and dependencies."""
    global config, ai_client, preset_manager, tool_registry
    
    try:
        # Load configuration
        config = load_config()
        setup_logging(config.log_level)
        
        # Initialize components
        ai_client = AIClient(config)
        preset_manager = PresetManager(config.config_dir)
        tool_registry = ToolRegistry(config)
        
    except Exception as e:
        display_error(f"Failed to initialize application: {e}")
        raise typer.Exit(1)


@app.command()
def ask(
    prompt: str = typer.Argument(
        ..., 
        help="Natural language description of what you want to do"
    ),
    files: Optional[List[str]] = typer.Option(
        None, 
        "--file", 
        "-f",
        help="Files to include in the context (can be used multiple times)"
    ),
    preset: Optional[str] = typer.Option(
        None,
        "--preset",
        "-p", 
        help="Use a predefined preset command"
    ),
    dry_run: bool = typer.Option(
        False,
        "--dry-run",
        "-d",
        help="Show what would be done without executing"
    ),
    interactive: bool = typer.Option(
        True,
        "--interactive/--no-interactive",
        "-i/-n",
        help="Enable/disable interactive mode for confirmations"
    ),
    output_format: str = typer.Option(
        "rich",
        "--format",
        help="Output format: rich, json, plain"
    ),
) -> None:
    """Ask the AI assistant to help with coding tasks.
    
    This is the main command that processes natural language requests
    and executes appropriate actions using AI function calling.
    
    Examples:
        ai-code ask "Create a Python function to calculate fibonacci numbers"
        ai-code ask "Refactor this class to use dependency injection" -f myclass.py
        ai-code ask "Add error handling to all functions" -f *.py
        ai-code ask "Generate unit tests" -p testing
    """
    if not config:
        initialize_app()
    
    try:
        # Handle preset commands
        if preset:
            preset_prompt = preset_manager.get_preset(preset)
            if preset_prompt:
                # Replace template variables in preset
                prompt = preset_manager.render_preset(preset_prompt, {
                    'prompt': prompt,
                    'files': files or []
                })
            else:
                display_error(f"Preset '{preset}' not found")
                raise typer.Exit(1)
        
        # Process the request
        console.print(Panel(
            Text(f"Processing: {prompt}", style="bold blue"),
            title="AI Coding Assistant",
            border_style="blue"
        ))
        
        # Get file context if specified
        file_context = []
        if files:
            file_context = _get_file_context(files)
        
        # Call AI with function calling
        response = ai_client.process_request(
            prompt=prompt,
            file_context=file_context,
            dry_run=dry_run,
            interactive=interactive
        )
        
        # Display results based on format
        if output_format == "json":
            console.print_json(response.model_dump_json())
        elif output_format == "plain":
            console.print(response.content)
        else:  # rich format
            _display_rich_response(response)
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
        raise typer.Exit(0)
    except Exception as e:
        display_error(f"Error processing request: {e}")
        raise typer.Exit(1)


@app.command()
def preset(
    action: str = typer.Argument(
        ...,
        help="Action: list, create, edit, delete, show"
    ),
    name: Optional[str] = typer.Argument(
        None,
        help="Preset name"
    ),
    template: Optional[str] = typer.Option(
        None,
        "--template",
        "-t",
        help="Preset template content"
    ),
) -> None:
    """Manage preset commands for common tasks.
    
    Presets allow you to save frequently used prompts and reuse them
    with template variable substitution.
    
    Examples:
        ai-code preset list
        ai-code preset create testing "Generate unit tests for {files}"
        ai-code preset show testing
        ai-code preset delete testing
    """
    if not config:
        initialize_app()
    
    try:
        if action == "list":
            presets = preset_manager.list_presets()
            if presets:
                console.print("[bold]Available Presets:[/bold]")
                for preset_name in presets:
                    console.print(f"  • {preset_name}")
            else:
                console.print("[yellow]No presets found[/yellow]")
                
        elif action == "create":
            if not name or not template:
                display_error("Both name and template are required for create")
                raise typer.Exit(1)
            preset_manager.create_preset(name, template)
            display_success(f"Preset '{name}' created successfully")
            
        elif action == "show":
            if not name:
                display_error("Preset name is required for show")
                raise typer.Exit(1)
            preset_content = preset_manager.get_preset(name)
            if preset_content:
                console.print(Panel(
                    preset_content,
                    title=f"Preset: {name}",
                    border_style="green"
                ))
            else:
                display_error(f"Preset '{name}' not found")
                raise typer.Exit(1)
                
        elif action == "delete":
            if not name:
                display_error("Preset name is required for delete")
                raise typer.Exit(1)
            if preset_manager.delete_preset(name):
                display_success(f"Preset '{name}' deleted successfully")
            else:
                display_error(f"Preset '{name}' not found")
                raise typer.Exit(1)
                
        else:
            display_error(f"Unknown action: {action}")
            raise typer.Exit(1)
            
    except Exception as e:
        display_error(f"Error managing presets: {e}")
        raise typer.Exit(1)


@app.command("config")
def config_cmd(
    action: str = typer.Argument(
        ...,
        help="Action: show, set, reset"
    ),
    key: Optional[str] = typer.Argument(
        None,
        help="Configuration key"
    ),
    value: Optional[str] = typer.Argument(
        None,
        help="Configuration value"
    ),
) -> None:
    """Manage configuration settings.
    
    Examples:
        ai-code config show
        ai-code config set openai_model gpt-4
        ai-code config reset
    """
    if not config:
        initialize_app()
    
    try:
        if action == "show":
            config.display()
        elif action == "set":
            if not key or not value:
                display_error("Both key and value are required for set")
                raise typer.Exit(1)
            config.set(key, value)
            display_success(f"Configuration updated: {key} = {value}")
        elif action == "reset":
            config.reset()
            display_success("Configuration reset to defaults")
        else:
            display_error(f"Unknown action: {action}")
            raise typer.Exit(1)
            
    except Exception as e:
        display_error(f"Error managing configuration: {e}")
        raise typer.Exit(1)


def _get_file_context(files: List[str]) -> List[dict]:
    """Get file content for context."""
    file_context = []
    for file_pattern in files:
        # Handle glob patterns
        if '*' in file_pattern:
            from glob import glob
            matched_files = glob(file_pattern)
        else:
            matched_files = [file_pattern]
        
        for file_path in matched_files:
            path = Path(file_path)
            if path.exists() and path.is_file():
                try:
                    content = path.read_text(encoding='utf-8')
                    file_context.append({
                        'path': str(path),
                        'content': content
                    })
                except Exception as e:
                    console.print(f"[yellow]Warning: Could not read {file_path}: {e}[/yellow]")
    
    return file_context


def _display_rich_response(response) -> None:
    """Display AI response in rich format."""
    # This will be implemented based on the response structure
    # from the AI client
    console.print(Panel(
        response.content,
        title="AI Response",
        border_style="green"
    ))


def main() -> None:
    """Main entry point for the CLI application."""
    try:
        app()
    except Exception as e:
        console.print(f"[red]Fatal error: {e}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
