"""OpenAI client wrapper for AI Coding CLI.

This module provides a wrapper around the OpenAI API with function calling
capabilities specifically designed for coding tasks.
"""

import json
import logging
from typing import Any, Dict, List, Optional, Union

from openai import OpenAI
from pydantic import BaseModel

from .config import Config
from .tools.registry import ToolRegistry
from .utils import display_error, display_info, display_warning

logger = logging.getLogger(__name__)


class AIResponse(BaseModel):
    """Response model for AI interactions.
    
    Attributes:
        content: The main response content
        function_calls: List of function calls made
        tokens_used: Number of tokens consumed
        model_used: Model that generated the response
        success: Whether the request was successful
        error: Error message if any
    """
    content: str
    function_calls: List[Dict[str, Any]] = []
    tokens_used: int = 0
    model_used: str = ""
    success: bool = True
    error: Optional[str] = None


class AIClient:
    """OpenAI client wrapper with function calling capabilities.
    
    This class handles all interactions with the OpenAI API, including
    function calling for coding tasks, context management, and response
    processing.
    """
    
    def __init__(self, config: Config):
        """Initialize the AI client.
        
        Args:
            config: Configuration object with API settings
        """
        self.config = config
        self.client = OpenAI(api_key=config.openai_api_key)
        self.tool_registry = None  # Will be set by CLI
        
        # System prompt for coding tasks
        self.system_prompt = """You are an expert AI coding assistant. You help developers with various coding tasks including:

- Writing new code and functions
- Refactoring and optimizing existing code  
- Debugging and fixing issues
- Adding documentation and tests
- Code analysis and review
- Converting between languages/formats
- Explaining complex code

You have access to various tools for file operations, code analysis, and other tasks. Always:
1. Understand the user's request thoroughly
2. Use appropriate tools to gather context when needed
3. Provide clear, well-documented, and efficient solutions
4. Follow best practices and coding standards
5. Explain your reasoning and approach
6. Ask for clarification if the request is ambiguous

When working with files, always check if they exist and read their content before making changes.
Be careful with destructive operations and ask for confirmation when appropriate."""
    
    def set_tool_registry(self, tool_registry: 'ToolRegistry') -> None:
        """Set the tool registry for function calling.
        
        Args:
            tool_registry: Registry containing available tools
        """
        self.tool_registry = tool_registry
    
    def process_request(
        self,
        prompt: str,
        file_context: Optional[List[Dict[str, str]]] = None,
        dry_run: bool = False,
        interactive: bool = True,
        max_iterations: int = 5
    ) -> AIResponse:
        """Process a user request with function calling.
        
        Args:
            prompt: User's natural language request
            file_context: Optional file content for context
            dry_run: If True, don't execute functions, just show what would be done
            interactive: If True, ask for confirmation before destructive operations
            max_iterations: Maximum number of function call iterations
            
        Returns:
            AIResponse object with results
        """
        try:
            # Validate configuration
            if not self.config.validate_required():
                return AIResponse(
                    content="Configuration validation failed",
                    success=False,
                    error="Missing required configuration"
                )
            
            # Build context message
            context_message = self._build_context_message(prompt, file_context)
            
            # Prepare messages
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": context_message}
            ]
            
            # Get available tools
            tools = self.tool_registry.get_openai_tools() if self.tool_registry else []
            
            total_tokens = 0
            function_calls = []
            
            # Main conversation loop with function calling
            for iteration in range(max_iterations):
                logger.info(f"AI request iteration {iteration + 1}")
                
                # Make API call
                response = self._make_api_call(messages, tools)
                
                if not response:
                    return AIResponse(
                        content="Failed to get response from OpenAI",
                        success=False,
                        error="API call failed"
                    )
                
                # Track token usage
                if hasattr(response, 'usage') and response.usage:
                    total_tokens += response.usage.total_tokens
                
                message = response.choices[0].message
                messages.append(message.model_dump())
                
                # Check if there are function calls
                if message.tool_calls:
                    # Process function calls
                    for tool_call in message.tool_calls:
                        function_result = self._execute_function_call(
                            tool_call,
                            dry_run=dry_run,
                            interactive=interactive
                        )
                        
                        function_calls.append({
                            'name': tool_call.function.name,
                            'arguments': json.loads(tool_call.function.arguments),
                            'result': function_result,
                            'dry_run': dry_run
                        })
                        
                        # Add function result to conversation
                        messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": json.dumps(function_result)
                        })
                else:
                    # No more function calls, we're done
                    return AIResponse(
                        content=message.content or "",
                        function_calls=function_calls,
                        tokens_used=total_tokens,
                        model_used=self.config.openai_model,
                        success=True
                    )
            
            # If we reach here, we hit max iterations
            display_warning(f"Reached maximum iterations ({max_iterations})")
            return AIResponse(
                content=messages[-1].get('content', ''),
                function_calls=function_calls,
                tokens_used=total_tokens,
                model_used=self.config.openai_model,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error processing request: {e}")
            return AIResponse(
                content="",
                success=False,
                error=str(e)
            )
    
    def _build_context_message(
        self,
        prompt: str,
        file_context: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """Build context message with prompt and file content.
        
        Args:
            prompt: User's request
            file_context: Optional file content
            
        Returns:
            Formatted context message
        """
        message_parts = [f"User Request: {prompt}"]
        
        if file_context:
            message_parts.append("\nFile Context:")
            for file_info in file_context:
                file_path = file_info.get('path', 'unknown')
                file_content = file_info.get('content', '')
                
                message_parts.append(f"\n--- File: {file_path} ---")
                message_parts.append(file_content)
                message_parts.append("--- End of File ---")
        
        return "\n".join(message_parts)
    
    def _make_api_call(
        self,
        messages: List[Dict[str, Any]],
        tools: List[Dict[str, Any]]
    ) -> Optional[Any]:
        """Make API call to OpenAI.
        
        Args:
            messages: Conversation messages
            tools: Available tools for function calling
            
        Returns:
            OpenAI response object or None if failed
        """
        try:
            call_params = {
                "model": self.config.openai_model,
                "messages": messages,
                "max_tokens": self.config.openai_max_tokens,
                "temperature": self.config.openai_temperature,
            }
            
            if tools:
                call_params["tools"] = tools
                call_params["tool_choice"] = "auto"
            
            response = self.client.chat.completions.create(**call_params)
            return response
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            display_error(f"OpenAI API call failed: {e}")
            return None
    
    def _execute_function_call(
        self,
        tool_call: Any,
        dry_run: bool = False,
        interactive: bool = True
    ) -> Dict[str, Any]:
        """Execute a function call.
        
        Args:
            tool_call: OpenAI tool call object
            dry_run: If True, don't actually execute
            interactive: If True, ask for confirmation
            
        Returns:
            Function execution result
        """
        try:
            function_name = tool_call.function.name
            arguments = json.loads(tool_call.function.arguments)
            
            display_info(f"Calling function: {function_name}")
            logger.info(f"Function call: {function_name} with args: {arguments}")
            
            if dry_run:
                return {
                    "success": True,
                    "message": f"DRY RUN: Would call {function_name} with {arguments}",
                    "dry_run": True
                }
            
            if not self.tool_registry:
                return {
                    "success": False,
                    "error": "Tool registry not available"
                }
            
            # Execute the function
            result = self.tool_registry.execute_tool(
                function_name,
                arguments,
                interactive=interactive
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Function execution failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def validate_connection(self) -> bool:
        """Validate connection to OpenAI API.
        
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            # Make a simple API call to test connection
            response = self.client.chat.completions.create(
                model=self.config.openai_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return True
            
        except Exception as e:
            logger.error(f"OpenAI connection validation failed: {e}")
            display_error(f"OpenAI connection failed: {e}")
            return False
