[project]
name = "ai-coding-cli"
version = "0.1.0"
description = "A natural language AI coding assistant CLI tool with OpenAI function calling"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    {name = "AI Coding CLI Team", email = "<EMAIL>"}
]
keywords = ["ai", "coding", "cli", "openai", "assistant", "function-calling"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "click>=8.2.1",
    "openai>=1.99.9",
    "pydantic>=2.11.7",
    "python-dotenv>=1.1.1",
    "rich>=14.1.0",
    "typer>=0.16.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
ai-code = "ai_coding_cli.cli:main"

[project.urls]
Homepage = "https://github.com/ai-coding-cli/ai-coding-cli"
Repository = "https://github.com/ai-coding-cli/ai-coding-cli"
Documentation = "https://ai-coding-cli.readthedocs.io"
"Bug Tracker" = "https://github.com/ai-coding-cli/ai-coding-cli/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/ai_coding_cli"]

[tool.black]
line-length = 79
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 79
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src/ai_coding_cli",
    "--cov-report=html",
    "--cov-report=term-missing",
    "--cov-fail-under=80",
]
